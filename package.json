{"name": "uk.ac.imperial.epicollect.five", "version": "86.2.2", "private": true, "description": "Free and easy-to-use mobile data-gathering platform", "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "test:unit": "vue-cli-service test:unit", "test:e2e": "vue-cli-service test:e2e", "lint": "vue-cli-service lint", "all-build": "ionic cap copy", "preall-build-prod": "vitest --run --silent", "all-build-debug": "export VUE_APP_MODE=WEB && ionic cap copy --inline", "all-build-prod": "export VUE_APP_MODE=WEB && export VUE_APP_DEBUG= && VUE_APP_BYPASS_UNIQUENESS= && ionic cap copy --prod --inline", "all-copy": "ionic cap copy --no-build", "android": "export VUE_APP_MODE=WEB && ionic cap copy android && ionic cap open android", "android-build": "export VUE_APP_MODE=WEB && ionic cap copy android", "dev-build": "ionic build --mode development --source-maps", "ios": "ionic cap copy ios && ionic cap open ios", "ios-build": "ionic cap copy ios", "preprod": "vitest --run --silent", "prod": "export VUE_APP_DEBUG= && ionic cap copy", "open-android": "ionic cap open android", "open-ios": "ionic cap open ios", "britest": "vitest --browser.name=chrome", "coverage": "vitest --coverage", "vitest-ui": "vitest --ui", "vitest": "vitest", "web": "export VUE_APP_MODE=WEB && ionic serve --no-open --external", "pwa": "export VUE_APP_MODE=PWA && export VUE_APP_DEBUG=1 && ionic serve --no-open -p 1234 --external", "pwa-build-prod": "export VUE_APP_MODE=PWA && export VUE_APP_DEBUG= && VUE_APP_BYPASS_UNIQUENESS= && ionic build", "pwa-build-dev": "export VUE_APP_MODE=PWA && export VUE_APP_DEBUG=1 && ionic build", "rollbar": "./rollbar-upload.sh"}, "dependencies": {"@awesome-cordova-plugins/wheel-selector": "^5.44.0", "@capacitor/action-sheet": "^7.0.0", "@capacitor/android": "^7.0.0", "@capacitor/app": "^7.0.0", "@capacitor/camera": "^7.0.0", "@capacitor/core": "^7.0.0", "@capacitor/device": "^7.0.0", "@capacitor/dialog": "^7.0.0", "@capacitor/filesystem": "^7.0.0", "@capacitor/geolocation": "^7.0.0", "@capacitor/haptics": "^7.0.0", "@capacitor/ios": "^7.0.0", "@capacitor/keyboard": "^7.0.0", "@capacitor/local-notifications": "^7.0.0", "@capacitor/network": "^7.0.0", "@capacitor/push-notifications": "^7.0.0", "@capacitor/share": "^7.0.0", "@capacitor/splash-screen": "^7.0.0", "@capacitor/status-bar": "^7.0.0", "@capacitor/toast": "^7.0.0", "@ionic-native/core": "^5.36.0", "@ionic-native/globalization": "^5.32.1", "@ionic/core": "^8.2.0", "@ionic/vue": "^8.2.0", "@ionic/vue-router": "^8.2.0", "axios": "1.7.4", "cordova-plugin-file": "^6.0.2", "cordova-plugin-file-transfer": "git+https://github.com/sitewaerts/cordova-plugin-file-transfer.git", "cordova-plugin-foreground-service": "git+https://<EMAIL>/epicollect/cordova-foreground-plugin.git", "cordova-plugin-geolocation": "^4.1.0", "cordova-plugin-globalization": "^1.11.0", "cordova-plugin-googleplus": "git+https://github.com/epicollect5/cordova-plugin-googleplus.git#fix-signout", "cordova-plugin-media": "github:epicollect5/cordova-plugin-media", "cordova-plugin-media-capture": "github:epicollect5/cordova-plugin-media-capture", "cordova-plugin-sign-in-with-apple": "^0.1.2", "cordova-sqlite-storage": "^2.6.0", "cordova-wheel-selector-plugin": "^1.1.7", "cordova.plugins.diagnostic": "github:epicollect5/cordova-diagnostic-plugin", "core-js": "^3.6.5", "date-fns": "^2.25.0", "flush-promises": "^1.0.2", "is-valid-coords": "^1.4.0", "jszip": "^3.7.1", "papaparse": "^5.3.2", "phonegap-plugin-barcodescanner": "^8.1.0", "pinia": "^2.1.4", "pinia-logger": "^1.3.12", "proj4": "^2.8.0", "register-service-worker": "^1.7.2", "rollbar": "^2.26.3", "slugify": "^1.6.5", "swiper": "^8.4.6", "uuid": "^8.3.2", "vitest": "^0.34.1", "vue": "^3.4.27", "vue-datepicker-next": "^1.0.2", "vue-router": "^4.3.2", "vue-scroll-picker": "^1.1.1", "vue3-dropzone": "^2.0.1", "vue3-scroll-picker": "^0.1.15"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.18.2", "@babel/eslint-plugin": "^7.17.7", "@capacitor/cli": "^7.0.0", "@originjs/vite-plugin-commonjs": "^1.0.3", "@pinia/testing": "^0.1.3", "@vitejs/plugin-vue": "^3.0.1", "@vitest/browser": "^0.34.2", "@vitest/coverage-istanbul": "^0.34.2", "@vitest/coverage-v8": "^0.34.1", "@vitest/ui": "^0.34.1", "@vue/cli-plugin-babel": "^5.0.6", "@vue/cli-plugin-e2e-cypress": "^5.0.6", "@vue/cli-plugin-eslint": "^5.0.6", "@vue/cli-plugin-pwa": "^5.0.8", "@vue/cli-plugin-router": "^5.0.6", "@vue/cli-plugin-unit-jest": "^5.0.6", "@vue/cli-service": "5.0.8", "@vue/compiler-sfc": "^3.0.0-0", "@vue/test-utils": "^2.4.1", "an-array-of-english-words": "^2.0.0", "an-array-of-german-words": "^1.2.0", "an-array-of-spanish-words": "^2.0.0", "cypress": "10.3.0", "eslint": "^7.32.0", "eslint-plugin-vue": "^8.0.3", "gulp": "^4.0.2", "gulp-shell": "^0.8.0", "jest": "^27.1.0", "jetifier": "^2.0.0", "jsdom": "^20.0.0", "playwright": "^1.37.1", "sass": "^1.63.3", "sass-loader": "^10.1.1", "terser-webpack-plugin": "^5.3.6", "typescript": "^5.1.3", "vue-cli-plugin-webpack-bundle-analyzer": "~4.0.0", "webdriverio": "^8.15.4"}}