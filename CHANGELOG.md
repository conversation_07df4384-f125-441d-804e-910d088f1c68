# Release Notes

## 86.2.2 - build 8622
- Using exact match when searching for projects opened by an App Link

## 86.2.1 - build 8621
- Updated build number to match Ionic 8 - Capacitor 6 versions
- Fixed wrong sorting of forms on the download entries page
- Fixed header for alert notification
- Added Slovenian translation
- Added warning about manual project addition on the projects page
- Fixed bugs and stability improvements


## 76.1.0 - build 7610
 - Potential fix for "Error code 1" on IOS by using private temp folder
 - Replaced toast Ionic library with Capacitor one due to bugs
 - Added timeout to DB opening due to warnings
 - Fixed bugs and stability improvements

## 7.0.4
 - Added Catalan translation
 - Fixed permissions issues on Android 11

## 7.0.3
 - UI improvements
 - Disabled Ionic 8 dynamic fonts as css zoom is used

## 7.0.1
 - Location popover UI improvements
 - Capacitor 6 update
 - Ionic 8 update
