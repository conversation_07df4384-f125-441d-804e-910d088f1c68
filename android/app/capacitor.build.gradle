// DO NOT EDIT THIS FILE! IT IS GENERATED EACH TIME "capacitor update" IS RUN

android {
  compileOptions {
      sourceCompatibility JavaVersion.VERSION_21
      targetCompatibility JavaVersion.VERSION_21
  }
}

apply from: "../capacitor-cordova-android-plugins/cordova.variables.gradle"
dependencies {
    implementation project(':capacitor-action-sheet')
    implementation project(':capacitor-app')
    implementation project(':capacitor-camera')
    implementation project(':capacitor-device')
    implementation project(':capacitor-dialog')
    implementation project(':capacitor-filesystem')
    implementation project(':capacitor-geolocation')
    implementation project(':capacitor-haptics')
    implementation project(':capacitor-keyboard')
    implementation project(':capacitor-local-notifications')
    implementation project(':capacitor-network')
    implementation project(':capacitor-push-notifications')
    implementation project(':capacitor-share')
    implementation project(':capacitor-splash-screen')
    implementation project(':capacitor-status-bar')
    implementation project(':capacitor-toast')
    implementation "com.google.android.gms:play-services-auth:15.0.1"
    implementation "com.google.android.gms:play-services-identity:15.0.1"
    implementation "com.android.support:support-v4:27.+"
    implementation "androidx.legacy:legacy-support-v4:1.0.0"
    implementation "androidx.appcompat:appcompat:1.3.1"
    implementation "com.android.support:support-v4:27.+"
}
apply from: "../../node_modules/phonegap-plugin-barcodescanner/src/android/barcodescanner.gradle"

if (hasProperty('postBuildExtras')) {
  postBuildExtras()
}
